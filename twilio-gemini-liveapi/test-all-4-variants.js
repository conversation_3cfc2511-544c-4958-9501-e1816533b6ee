#!/usr/bin/env node

import WebSocket from 'ws';

// Test configurations for all 4 variants
const testConfigs = [
    {
        name: "INCOMING TEST MODE",
        endpoint: "local-audio-session",
        config: {
            voice: '<PERSON><PERSON>',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            scriptType: 'incoming',
            scriptId: 'incoming-1',
            targetName: 'Test User',
            targetPhoneNumber: '+420733154483',
            isTestMode: true
        }
    },
    {
        name: "OUTBOUND TEST MODE", 
        endpoint: "local-audio-session",
        config: {
            voice: '<PERSON><PERSON>',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            scriptType: 'outbound',
            scriptId: 'campaign-1',
            targetName: 'Test User',
            targetPhoneNumber: '+420733154483',
            isTestMode: true
        }
    },
    {
        name: "INCOMING REAL MODE",
        endpoint: "media-stream",
        config: {
            voice: '<PERSON><PERSON>',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            scriptType: 'incoming',
            scriptId: 'incoming-1',
            targetName: 'Test User',
            targetPhoneNumber: '+420733154483',
            isTestMode: false
        },
        expectConnection: true, // Real mode just connects, doesn't expect session-started
        timeout: 3000 // Shorter timeout for real mode
    },
    {
        name: "OUTBOUND REAL MODE",
        endpoint: "media-stream", 
        config: {
            voice: 'Kore',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            scriptType: 'outbound',
            scriptId: 'campaign-1',
            targetName: 'Test User',
            targetPhoneNumber: '+420733154483',
            isTestMode: false
        },
        expectConnection: true, // Real mode just connects, doesn't expect session-started
        timeout: 3000 // Shorter timeout for real mode
    }
];

async function testVariant(testConfig) {
    return new Promise((resolve) => {
        console.log(`\n🧪 Testing ${testConfig.name}...`);
        
        const startTime = Date.now();
        const ws = new WebSocket(`ws://localhost:3101/${testConfig.endpoint}`);
        let sessionStarted = false;
        let connected = false;
        
        const timeout = setTimeout(() => {
            const duration = Date.now() - startTime;
            if (testConfig.expectConnection && connected) {
                console.log(`✅ ${testConfig.name}: SUCCESS (Connected for ${duration}ms)`);
                resolve({ success: true, duration, variant: testConfig.name });
            } else if (!testConfig.expectConnection) {
                console.log(`❌ ${testConfig.name}: TIMEOUT (${duration}ms) - No session-started received`);
                resolve({ success: false, duration, variant: testConfig.name, error: 'timeout' });
            } else {
                console.log(`❌ ${testConfig.name}: TIMEOUT (${duration}ms) - No connection`);
                resolve({ success: false, duration, variant: testConfig.name, error: 'no_connection' });
            }
            ws.close();
        }, testConfig.timeout || 8000);

        ws.on('open', () => {
            connected = true;
            console.log(`  ✅ WebSocket connected to ${testConfig.endpoint}`);
            
            // Send start-session message for test modes
            if (testConfig.config.isTestMode) {
                console.log(`  📤 Sending test configuration...`);
                ws.send(JSON.stringify({
                    type: 'start-session',
                    ...testConfig.config
                }));
            }
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log(`  📨 Received: ${message.type}`);
                
                if (message.type === 'session-started') {
                    sessionStarted = true;
                    const duration = Date.now() - startTime;
                    console.log(`  ✅ Session started successfully! (${duration}ms)`);
                    
                    // Send test audio data
                    console.log(`  📤 Sending test audio data...`);
                    ws.send(JSON.stringify({
                        type: 'audio-data',
                        audioData: Buffer.from('test audio data').toString('base64')
                    }));
                    
                    // Close after a short delay
                    setTimeout(() => {
                        const finalDuration = Date.now() - startTime;
                        console.log(`✅ ${testConfig.name}: SUCCESS (${finalDuration}ms)`);
                        clearTimeout(timeout);
                        resolve({ success: true, duration: finalDuration, variant: testConfig.name });
                        ws.close();
                    }, 1000);
                }
            } catch (error) {
                console.log(`  ⚠️ Error parsing message: ${error.message}`);
            }
        });

        ws.on('close', (code, reason) => {
            const duration = Date.now() - startTime;
            console.log(`  🔌 WebSocket closed - Code: ${code}, Duration: ${duration}ms`);
            
            if (!sessionStarted && !testConfig.expectConnection) {
                console.log(`❌ ${testConfig.name}: FAILED - Session closed without starting`);
                clearTimeout(timeout);
                resolve({ success: false, duration, variant: testConfig.name, error: 'premature_close' });
            }
        });

        ws.on('error', (error) => {
            const duration = Date.now() - startTime;
            console.log(`  ❌ WebSocket error: ${error.message}`);
            console.log(`❌ ${testConfig.name}: ERROR (${duration}ms)`);
            clearTimeout(timeout);
            resolve({ success: false, duration, variant: testConfig.name, error: error.message });
        });
    });
}

async function runAllTests() {
    console.log('🚀 Testing All 4 Twilio Gemini Variants...\n');
    
    const results = [];
    
    for (const testConfig of testConfigs) {
        const result = await testVariant(testConfig);
        results.push(result);
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Print summary
    console.log('\n📊 TEST RESULTS SUMMARY:');
    console.log('=' .repeat(50));
    
    let successCount = 0;
    results.forEach(result => {
        const status = result.success ? '✅ SUCCESS' : '❌ FAILED';
        const error = result.error ? ` (${result.error})` : '';
        console.log(`${status}: ${result.variant} - ${result.duration}ms${error}`);
        if (result.success) successCount++;
    });
    
    console.log('=' .repeat(50));
    console.log(`🎯 Overall Result: ${successCount}/${results.length} variants working`);
    
    if (successCount === results.length) {
        console.log('🎉 ALL 4 VARIANTS ARE WORKING PERFECTLY!');
    } else {
        console.log('⚠️  Some variants need attention');
    }
}

// Run the tests
runAllTests().catch(console.error);
