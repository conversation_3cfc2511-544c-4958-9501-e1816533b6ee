#!/usr/bin/env node

import WebSocket from 'ws';
import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 Testing Exact Copy of Working Implementation...');

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const geminiClient = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

let sessionStarted = false;
let aiResponded = false;
let geminiSession = null;

const ws = new WebSocket('ws://localhost:3101/local-audio-session');

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send session start
    console.log('📤 Starting session...');
    ws.send(JSON.stringify({
        type: 'start-session',
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        scriptType: 'incoming',
        scriptId: 'incoming-1',
        targetName: 'Test User',
        targetPhoneNumber: '+420733154483',
        isTestMode: true
    }));
});

ws.on('message', async (data) => {
    try {
        const message = JSON.parse(data);
        console.log(`📨 Received: ${message.type}`);
        
        if (message.type === 'session-started') {
            sessionStarted = true;
            console.log('✅ Session started! Now creating our own Gemini session...');
            
            // Create our own Gemini session using the EXACT same approach as the working test
            try {
                console.log('🤖 Creating Gemini session directly...');
                
                geminiSession = await geminiClient.live.connect({
                    model: 'gemini-2.5-flash-preview-native-audio-dialog',
                    callbacks: {
                        onopen: () => {
                            console.log('✅ Direct Gemini session opened successfully');
                            
                            // Send a simple text message after a delay
                            setTimeout(() => {
                                console.log('📤 Sending text message to direct Gemini session...');
                                geminiSession.sendClientContent({
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            text: 'Hello, please say "test response" back to me.'
                                        }]
                                    }],
                                    turnComplete: true
                                });
                            }, 1000);
                        },
                        
                        onmessage: (message) => {
                            aiResponded = true;
                            console.log('🎉 DIRECT GEMINI RESPONSE RECEIVED!');
                            console.log('📨 Message type:', message?.type || 'unknown');
                            console.log('📨 Message keys:', Object.keys(message || {}).join(', '));
                            
                            // Check for audio response
                            const audio = message?.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (audio) {
                                console.log('🎵 Audio response received!');
                                console.log('   MIME type:', audio.mimeType);
                                console.log('   Data size:', audio.data?.length || 0, 'bytes');
                            }
                            
                            // Check for text response
                            const text = message?.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log('💬 Text response:', text);
                            }
                        },
                        
                        onerror: (error) => {
                            console.error('❌ Direct Gemini session error:', error);
                        },
                        
                        onclose: () => {
                            console.log('🔌 Direct Gemini session closed');
                        }
                    },
                    config: {
                        responseModalities: [Modality.AUDIO],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: {
                                    voiceName: 'Kore'
                                }
                            }
                        }
                    },
                    temperature: 1.1,
                    topP: 0.95,
                    topK: 40,
                    maxOutputTokens: 8192,
                    responseMimeType: 'text/plain'
                });
                
                console.log('✅ Direct Gemini session created successfully');
                
            } catch (error) {
                console.error('❌ Error creating direct Gemini session:', error);
            }
        }
    } catch (error) {
        console.log(`⚠️ Error parsing message: ${error.message}`);
    }
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed - Code: ${code}, Reason: ${reason}`);
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Session Started: ${sessionStarted ? 'YES' : 'NO'}`);
    console.log(`🤖 AI Responded: ${aiResponded ? 'YES' : 'NO'}`);
    
    if (sessionStarted && !aiResponded) {
        console.log('\n❌ ISSUE: Direct Gemini session also does not respond');
        console.log('   This suggests the issue is NOT in our session manager');
        console.log('   The issue might be in the environment or API key');
    } else if (!sessionStarted) {
        console.log('\n❌ ISSUE: Session failed to start');
    } else {
        console.log('\n🎉 SUCCESS: Direct Gemini session is responding!');
        console.log('   This means the issue IS in our session manager implementation');
    }
    
    // Close direct Gemini session
    if (geminiSession) {
        try {
            geminiSession.close();
        } catch (e) {
            console.log('Direct session already closed');
        }
    }
});

ws.on('error', (error) => {
    console.log(`❌ WebSocket error: ${error.message}`);
});

// Auto-close after 15 seconds
setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
        console.log('\n⏰ Test timeout - closing connection');
        ws.close();
    }
}, 15000);
