import { Modality } from '../gemini/client.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Session Manager for Gemini connections with recovery
export class SessionManager {
    constructor(contextManager, geminiClient) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new Set();
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new Map();
    }

    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            console.log(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            console.log(`🔍 [${callSid}] config.model = "${config.model}"`);
            console.log(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            console.log(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            console.log(`🔍 [${callSid}] ==========================================`);

            // Capture 'this' context for callbacks
            const self = this;

            // Store the session directly in connectionData to ensure proper scope
            connectionData.geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        connectionData.isSessionActive = true;
                        
                        // Initialize session metrics
                        this.sessionMetrics.set(callSid, {
                            startTime: Date.now(),
                            messagesReceived: 0,
                            messagesSent: 0,
                            recoveryCount: 0,
                            lastActivity: Date.now()
                        });
                        
                        // Save initial context
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...connectionData,
                            conversationLog: [],
                            fullTranscript: []
                        });

                        // Note: Initial message will be sent after session creation
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        connectionData.isSessionActive = false;
                        connectionData.geminiSessionError = error.message;

                        console.log(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Gemini session closed`);
                        connectionData.isSessionActive = false;

                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = connectionData.twilioWs && connectionData.twilioWs.readyState === 1; // WebSocket.OPEN
                        const isLocalTestingActive = connectionData.localWs && connectionData.localWs.readyState === 1;

                        if (isUnexpectedClose || isLocalTestingActive) {
                            console.log(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            console.log(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            console.log(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message) => {
                        try {
                            // Log Gemini API messages for debugging (excluding audio packets) - EXACT COPY FROM OLD IMPLEMENTATION
                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (!hasAudio) {
                                console.log(`📨 [${callSid}] Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                            } else {
                                console.log(`🎵 [${callSid}] Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);
                            }

                            // Enhanced message validation and handling - EXACT COPY FROM OLD IMPLEMENTATION
                            if (!message || !message.serverContent) {
                                console.warn(`⚠️ [${callSid}] Received invalid message structure`);
                                return;
                            }

                            // Update metrics
                            const metrics = self.sessionMetrics.get(callSid);
                            if (metrics) {
                                metrics.messagesReceived++;
                                metrics.lastActivity = Date.now();
                            }

                            // Handle audio response from Gemini with validation - EXACT COPY FROM OLD IMPLEMENTATION
                            const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                            if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                // Validate audio data before processing
                                if (!audio.data || audio.data.length === 0) {
                                    console.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
                                    return;
                                }

                                // Send audio to Twilio if connection is active
                                if (connectionData.twilioWs && connectionData.twilioWs.readyState === 1) { // WebSocket.OPEN
                                    try {
                                        const audioDelta = {
                                            event: 'media',
                                            streamSid: connectionData.streamSid,
                                            media: {
                                                payload: audio.data
                                            }
                                        };
                                        connectionData.twilioWs.send(JSON.stringify(audioDelta));
                                        console.log(`🔊 [${callSid}] Sent audio to Twilio (${audio.data.length} chars)`);
                                    } catch (audioError) {
                                        console.error(`❌ [${callSid}] Error sending audio to Twilio:`, audioError);
                                    }
                                }

                                // Send audio to local WebSocket if connection is active
                                if (connectionData.localWs && connectionData.localWs.readyState === 1) { // WebSocket.OPEN
                                    try {
                                        connectionData.localWs.send(JSON.stringify({
                                            type: 'audio-response',
                                            audio: {
                                                mimeType: audio.mimeType,
                                                data: audio.data
                                            }
                                        }));
                                        console.log(`🔊 [${callSid}] Sent audio to local WebSocket (${audio.data.length} chars)`);
                                    } catch (audioError) {
                                        console.error(`❌ [${callSid}] Error sending audio to local WebSocket:`, audioError);
                                    }
                                }
                            }

                            // Handle text response for summary collection and conversation logging - EXACT COPY FROM OLD IMPLEMENTATION
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Gemini response: ${text.substring(0, 100)}...`);

                                // Update AI responsiveness tracking
                                connectionData.lastAIResponse = Date.now();
                                connectionData.responseTimeouts = 0; // Reset timeout counter
                                connectionData.connectionQuality = 'good';
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing Gemini message:`, error);
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: 'text/plain'
            });

            return connectionData.geminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error creating Gemini session:`, error);
            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback (like old implementation)

    // Send initial message to AI
    async sendInitialMessage(geminiSession, aiInstructions) {
        try {
            if (geminiSession && aiInstructions) {
                await geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: aiInstructions
                        }]
                    }],
                    turnComplete: true
                });
                console.log('📤 Initial AI instructions sent');
            }
        } catch (error) {
            console.error('❌ Error sending initial message:', error);
        }
    }

    // Send text to Gemini session
    async sendTextToGemini(callSid, geminiSession, text) {
        try {
            console.log(`🔍 [${callSid}] Sending text to Gemini: "${text}"`);
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: text
                    }]
                }],
                turnComplete: false
            });
            console.log(`✅ [${callSid}] Text sent to Gemini successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending text to Gemini:`, error);
        }
    }

    // Send turn complete signal to Gemini (user finished speaking)
    async sendTurnComplete(callSid, geminiSession) {
        try {
            console.log(`🔍 [${callSid}] Sending turn complete signal to Gemini...`);
            await geminiSession.sendClientContent({
                turnComplete: true
            });
            console.log(`✅ [${callSid}] Turn complete signal sent successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending turn complete:`, error);
        }
    }

    // Send audio to Gemini session
    async sendAudioToGemini(callSid, geminiSession, audioBuffer) {
        try {
            console.log(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);

            if (!geminiSession || !audioBuffer) {
                console.log(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
                return;
            }

            console.log(`🔍 [${callSid}] Updating metrics...`);
            // Update metrics
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent++;
                metrics.lastActivity = Date.now();
            }

            console.log(`🔍 [${callSid}] Converting audio format - audioProcessor exists: ${!!this.audioProcessor}`);
            // Convert Twilio audio to Gemini format
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer);
            console.log(`🔍 [${callSid}] PCM conversion complete - buffer size: ${pcmBuffer.length}`);

            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
            console.log(`🔍 [${callSid}] Float32 conversion complete - array length: ${float32Data.length}`);

            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
            console.log(`🔍 [${callSid}] Audio blob created - size: ${audioBlob.data?.length || 'N/A'}`);

            console.log(`🔍 [${callSid}] Sending audio to Gemini session...`);
            // Send to Gemini
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        inlineData: audioBlob
                    }]
                }],
                turnComplete: false
            });
            console.log(`✅ [${callSid}] Audio sent to Gemini successfully`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending audio to Gemini:`, error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid, reason) {
        if (this.recoveryInProgress.has(callSid)) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }

        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount++;
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            console.log(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid, connectionData, summaryPrompt) {
        try {
            console.log(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData.geminiSession) {
                console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // Send summary request
            await connectionData.geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: summaryPrompt
                    }]
                }],
                turnComplete: true
            });

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating summary:`, error);
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid) {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid) {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        console.log(`🧹 [${callSid}] Session manager cleanup completed`);
    }
}
