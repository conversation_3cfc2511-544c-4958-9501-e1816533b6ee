import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;

// Available Gemini voices with characteristics
export const AVAILABLE_GEMINI_VOICES = {
    'Aoede': {
        name: '<PERSON>oede',
        gender: 'Female',
        characteristics: 'bright, neutral narrator',
        pitch: 'neutral',
        timbre: 'bright',
        persona: 'narrator'
    },
    'Puck': {
        name: 'Puck',
        gender: 'Male',
        characteristics: 'lively, higher tenor',
        pitch: 'higher',
        timbre: 'lively',
        persona: 'energetic'
    },
    'Charon': {
        name: '<PERSON><PERSON>',
        gender: 'Male',
        characteristics: 'deep, warm baritone',
        pitch: 'deep',
        timbre: 'warm',
        persona: 'authoritative'
    },
    '<PERSON>re': {
        name: '<PERSON><PERSON>',
        gender: 'Female',
        characteristics: 'soft alto, empathetic',
        pitch: 'alto',
        timbre: 'soft',
        persona: 'empathetic'
    },
    '<PERSON>rir': {
        name: '<PERSON><PERSON>r',
        gender: 'Male',
        characteristics: 'assertive mid-range',
        pitch: 'mid-range',
        timbre: 'assertive',
        persona: 'confident'
    },
    '<PERSON><PERSON>': {
        name: '<PERSON><PERSON>',
        gender: 'Female',
        characteristics: 'clear RP-style announcer',
        pitch: 'clear',
        timbre: 'professional',
        persona: 'announcer'
    },
    'Orus': {
        name: '<PERSON><PERSON>',
        gender: 'Male',
        characteristics: 'relaxed, breathy tenor',
        pitch: 'tenor',
        timbre: 'breathy',
        persona: 'relaxed'
    },
    'Zephyr': {
        name: 'Zephyr',
        gender: 'Female',
        characteristics: 'airy, youthful soprano',
        pitch: 'soprano',
        timbre: 'airy',
        persona: 'youthful'
    }
};

// Voice mapping for different languages/accents and legacy compatibility
export const VOICE_MAPPING = {
    // Legacy voice mappings (for backward compatibility)
    'shimmer': 'Orus',
    'alloy': 'Puck',
    'echo': 'Charon',
    'fable': 'Kore',
    'onyx': 'Fenrir',
    'nova': 'Aoede',
    // Gender-based mappings
    'female': 'Kore',
    'male': 'Orus',
    'professional': 'Leda',
    'youthful': 'Zephyr',
    'authoritative': 'Charon',
    'energetic': 'Puck'
};

// Import configuration
import { config, getConfigValue } from '../config/config.js';

// Available Gemini models (configurable)
export const AVAILABLE_GEMINI_MODELS = {
    'gemini-2.5-flash-preview-native-audio-dialog': {
        name: 'Gemini 2.5 Flash - Native Audio Dialog',
        description: 'Recommended default for voice apps. Outputs text and 24 kHz speech in 30 HD voices across 24 languages',
        status: 'Private preview',
        supportsAudio: true,
        quality: '★★★',
        region: 'global'
    },
    'gemini-2.0-flash-live-001': {
        name: 'Gemini 2.0 Flash Live (001)',
        description: 'GA/billing-enabled half-cascade audio. Native audio in, server-side TTS out',
        status: 'Public preview (billing on)',
        supportsAudio: true,
        quality: '★★',
        region: 'global, us-central1, EU4'
    }
};

// Function to map voice names and validate
export function getValidGeminiVoice(requestedVoice, defaultVoice = null) {
    if (!defaultVoice) {
        defaultVoice = getConfigValue('ai.gemini.defaultVoice', 'Kore');
    }

    if (!requestedVoice) {
        console.log(`🎤 No voice specified, using default: ${defaultVoice} (${AVAILABLE_GEMINI_VOICES[defaultVoice]?.characteristics || 'unknown'})`);
        return defaultVoice;
    }

    // Check if it's already a valid Gemini voice
    if (AVAILABLE_GEMINI_VOICES[requestedVoice]) {
        const voiceInfo = AVAILABLE_GEMINI_VOICES[requestedVoice];
        console.log(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return requestedVoice;
    }

    // Check if it's an OpenAI voice or other mapping that needs conversion
    if (VOICE_MAPPING[requestedVoice]) {
        const mappedVoice = VOICE_MAPPING[requestedVoice];
        const voiceInfo = AVAILABLE_GEMINI_VOICES[mappedVoice];
        console.log(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return mappedVoice;
    }

    // Default fallback
    const defaultInfo = AVAILABLE_GEMINI_VOICES[defaultVoice];
    console.log(`⚠️ Unknown voice '${requestedVoice}', using default: ${defaultVoice} (${defaultInfo?.characteristics || 'unknown'})`);
    return defaultVoice;
}

// Function to validate and get Gemini model
export function getValidGeminiModel(requestedModel, defaultModel = null) {
    if (!defaultModel) {
        defaultModel = getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog');
    }

    if (!requestedModel) {
        console.log(`🤖 No model specified, using default: ${defaultModel}`);
        return defaultModel;
    }

    // Check if it's a valid Gemini model
    if (AVAILABLE_GEMINI_MODELS[requestedModel]) {
        console.log(`🤖 Using requested model: ${requestedModel}`);
        return requestedModel;
    }

    // Default fallback
    console.log(`⚠️ Unknown model '${requestedModel}', using default: ${defaultModel}`);
    return defaultModel;
}

// Initialize Gemini client
export function initializeGeminiClient(apiKey) {
    if (!apiKey) {
        console.error('❌ GEMINI_API_KEY is required');
        return null;
    }

    try {
        const client = new GoogleGenAI({
            apiKey: apiKey
        });
        console.log('🤖 Gemini client initialized successfully');
        return client;
    } catch (error) {
        console.error('❌ Error initializing Gemini client:', error);
        return null;
    }
}

export { Modality };
